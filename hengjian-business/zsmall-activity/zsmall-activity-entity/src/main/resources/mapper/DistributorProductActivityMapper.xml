<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.DistributorProductActivityMapper">

    <!-- DistributorProductActivityDetails的ResultMap -->
    <resultMap id="DistributorProductActivityDetailsMap" type="com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityDetails">
        <!-- 主表字段映射 -->
        <id property="id" column="id"/>
        <result property="supplierActivityId" column="supplier_activity_id"/>
        <result property="supplierActivityCode" column="supplier_activity_code"/>
        <result property="distributorTenantId" column="distributor_tenant_id"/>
        <result property="distributorActivityCode" column="distributor_activity_code"/>
        <result property="activityType" column="activity_type"/>
        <result property="activityState" column="activity_state"/>
        <result property="productName" column="product_name"/>
        <result property="productImg" column="product_img"/>
        <result property="productCode" column="product_code"/>
        <result property="productSkuCode" column="product_sku_code"/>
        <result property="productSku" column="product_sku"/>
        <result property="activeStartTime" column="active_start_time"/>
        <result property="activeEndTime" column="active_end_time"/>
        <result property="site" column="site"/>
        <result property="currencySymbol" column="currency_symbol"/>
        <result property="activityDay" column="activity_day"/>
        <result property="freeStoragePeriod" column="free_storage_period"/>
        <result property="supportedLogistics" column="supported_logistics"/>
        <result property="quantityMinimum" column="quantity_minimum"/>
        <result property="depositPaidTotal" column="deposit_paid_total"/>
        <result property="storageFeePaidTotal" column="storage_fee_paid_total"/>
        <result property="distributorActivityStorageFee" column="distributor_activity_storage_fee"/>
        <result property="delFlag" column="del_flag"/>
        <result property="exceptionCode" column="exception_code"/>
        <result property="pickupQuantityLocked" column="pickup_quantity_locked"/>
        <result property="dropShippingQuantityLocked" column="drop_shipping_quantity_locked"/>
        <result property="pickupLockedUsed" column="pickup_locked_used"/>
        <result property="dropShippingLockedUsed" column="drop_shipping_locked_used"/>
        <result property="orderedTotal" column="ordered_total"/>

        <!-- 价格表字段映射 -->
        <result property="distributorActivityUnitPrice" column="distributor_activity_unit_price"/>
        <result property="distributorActivityOperationFee" column="distributor_activity_operation_fee"/>
        <result property="distributorActivityFinalDeliveryFee" column="distributor_activity_final_delivery_fee"/>
        <result property="distributorActivityPickUpPrice" column="distributor_activity_pick_up_price"/>
        <result property="distributorActivityDropShippingPrice" column="distributor_activity_drop_shipping_price"/>

        <!-- 库存信息集合映射 -->
        <collection property="distributorProductActivityStocks" ofType="com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock">
            <id property="id" column="stock_id"/>
            <result property="distributorActivityCode" column="stock_distributor_activity_code"/>
            <result property="distributorActivityId" column="stock_distributor_activity_id"/>
            <result property="supplierActivityStockId" column="supplier_activity_stock_id"/>
            <result property="warehouseSystemCode" column="warehouse_system_code"/>
            <result property="warehouseCode" column="warehouse_code"/>
            <result property="supportedLogistics" column="stock_supported_logistics"/>
            <result property="quantityTotal" column="quantity_total"/>
            <result property="quantitySold" column="quantity_sold"/>
            <result property="quantitySurplus" column="quantity_surplus"/>
            <result property="delFlag" column="stock_del_flag"/>
        </collection>
    </resultMap>


    <select id="selectListResponseDTO"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity">
        select *
        from distributor_product_activity dpa
        where del_flag=0
        <if test="wa.productName != null and wa.productName != ''">
            and dpa.product_name like concat('%',#{wa.productName},'%')
        </if>
        <if test="wa.productCode != null and wa.productCode != ''">
            and dpa.product_code like concat('%',#{wa.productCode},'%')
        </if>
        <if test="wa.productSkuCode != null and wa.productSkuCode != ''">
            and dpa.product_sku_code like concat('%',#{wa.productSkuCode},'%')
        </if>
        <if test="wa.distributorTenantId != null and wa.distributorTenantId != ''">
            and dpa.distributor_tenant_id like concat( '%',#{wa.distributorTenantId}, '%' )
        </if>
        <if test="wa.activityType != null and wa.activityType != ''">
            and dpa.activity_type = #{wa.activityType}
        </if>
        <if test="wa.activityState != null and wa.activityState != ''">
            and dpa.activity_state=#{wa.activityState}
        </if>
        <if test="wa.site != null and wa.site != ''">
            and site=#{wa.site}
        </if>
        order by create_time desc
    </select>

    <select id="selectDistributorProductActivityDetailsExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.DistributorProductActivityDetailsExportDTO">
        select
        dpa.distributor_activity_code,
        dpa.activity_type,
        dpa.product_sku_code,
        dpa.supported_logistics,
        dpa.deposit_paid_total,
        dpa.storage_fee_paid_total,
        dpas.warehouse_code,
        dpas.quantity_total,
        dpas.quantity_surplus
        from distributor_product_activity dpa
        left join distributor_product_activity_stock dpas on dpa.id = dpas.distributor_activity_id and dpa.del_flag=0
        where dpa.del_flag = 0
        <if test="wa.productName != null and wa.productName != ''">
            and dpa.product_name like concat('%',#{wa.productName},'%')
        </if>
        <if test="wa.productCode != null and wa.productCode != ''">
            and dpa.product_code like concat('%',#{wa.productCode},'%')
        </if>
        <if test="wa.productSkuCode != null and wa.productSkuCode != ''">
            and dpa.product_sku_code like concat('%',#{wa.productSkuCode},'%')
        </if>
        <if test="wa.distributorTenantId != null and wa.distributorTenantId != ''">
            and dpa.distributor_tenant_id = #{wa.distributorTenantId}
        </if>
        <if test="wa.activityType != null and wa.activityType != ''">
            and dpa.activity_type = #{wa.activityType}
        </if>
        <if test="wa.activityState != null and wa.activityState != ''">
            and dpa.activity_state=#{wa.activityState}
        </if>
        <if test="wa.site != null and wa.site != ''">
            and site=#{wa.site}
        </if>
        <if test="wa.distributorActivityCodes != null and wa.distributorActivityCodes.size() != 0">
            and dpa.distributor_activity_code in
            <foreach collection="wa.distributorActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by dpa.id desc
    </select>

    <select id="selectDistributorProductActivityListExport"
            resultType="com.zsmall.activity.entity.domain.dto.productActivity.export.DistributorProductActivityListExportDTO">
        select
        dpa.distributor_activity_code,
        dpa.activity_type,
        dpa.product_name,
        dpa.product_sku_code,
        dpa.site,
        dpa.currency_symbol,
        dpa.pickup_quantity_locked,
        dpa.drop_shipping_quantity_locked,
        dpa.pickup_locked_used,
        dpa.drop_shipping_locked_used,
        dpap.distributor_activity_unit_price,
        dpap.distributor_activity_operation_fee,
        dpap.distributor_activity_final_delivery_fee,
        dpa.quantity_minimum,
        dpa.distributor_activity_storage_fee,
        dpa.free_storage_period,
        dpa.activity_day,
        dpa.active_start_time,
        dpa.active_end_time,
        dpa.activity_state
        from distributor_product_activity dpa
        left join distributor_product_activity_price dpap  on dpa.id = dpap.distributor_activity_id and dpap.del_flag = 0
        where dpa.del_flag = 0
        <if test="wa.productName != null and wa.productName != ''">
            and dpa.product_name like concat('%',#{wa.productName},'%')
        </if>
        <if test="wa.productCode != null and wa.productCode != ''">
            and dpa.product_code like concat('%',#{wa.productCode},'%')
        </if>
        <if test="wa.productSkuCode != null and wa.productSkuCode != ''">
            and dpa.product_sku_code like concat('%',#{wa.productSkuCode},'%')
        </if>
        <if test="wa.distributorTenantId != null and wa.distributorTenantId != ''">
            and dpa.distributor_tenant_id = #{wa.distributorTenantId}
        </if>
        <if test="wa.activityType != null and wa.activityType != ''">
            and dpa.activity_type = #{wa.activityType}
        </if>
        <if test="wa.activityState != null and wa.activityState != ''">
            and dpa.activity_state=#{wa.activityState}
        </if>
        <if test="wa.site != null and wa.site != ''">
            and site=#{wa.site}
        </if>
        <if test="wa.distributorActivityCodes != null and wa.distributorActivityCodes.size() != 0">
            and dpa.distributor_activity_code in
            <foreach collection="wa.distributorActivityCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by dpa.id desc
    </select>

    <update id="updateDistributorProductActivityException">
        update distributor_product_activity dpa
            inner join supplier_product_activity spa on dpa.supplier_activity_id = spa.id
            inner join supplier_product_activity_stock spas on spa.id = spas.supplier_activity_id
        set dpa.exception_code = #{i},
        spas.exception_code = #{i}
        where spa.product_sku_code = #{productSkuCode}
        <if test="warehouseSystemCode != null and warehouseSystemCode != ''">
            and spas.warehouse_system_code = #{warehouseSystemCode}
        </if>
    </update>

    <select id="getDistributorAvailableActivesBySku" resultMap="DistributorProductActivityDetailsMap">
        SELECT
            dpa.*,
            dpap.distributor_activity_unit_price,
            dpap.distributor_activity_operation_fee,
            dpap.distributor_activity_final_delivery_fee,
            dpap.distributor_activity_pick_up_price,
            dpap.distributor_activity_drop_shipping_price,
            dpas.id as stock_id,
            dpas.distributor_activity_code as stock_distributor_activity_code,
            dpas.distributor_activity_id as stock_distributor_activity_id,
            dpas.supplier_activity_stock_id,
            dpas.warehouse_system_code,
            dpas.warehouse_code,
            dpas.supported_logistics as stock_supported_logistics,
            dpas.quantity_total,
            dpas.quantity_sold,
            dpas.quantity_surplus,
            dpas.del_flag as stock_del_flag
        FROM distributor_product_activity dpa
        inner join product_sku ps on dpa.product_sku_code = ps.product_sku_code and ps.del_flag = 0
        INNER JOIN distributor_product_activity_price dpap ON dpa.id = dpap.distributor_activity_id AND dpap.del_flag = 0
        INNER JOIN distributor_product_activity_stock dpas ON dpa.id = dpas.distributor_activity_id AND dpas.del_flag = 0
        WHERE dpa.del_flag = 0
            AND dpa.product_sku_code = #{productSkuCode}
            AND ps.shelf_state='OnShelf'
            AND dpa.site = #{site}
            AND dpa.activity_state = 'InProgress'
            AND dpas.quantity_surplus > 0
            AND dpa.exception_code = 0
        ORDER BY dpa.id DESC
    </select>

    <select id="getDistributorAvailableActivesBySpu" resultMap="DistributorProductActivityDetailsMap">
        SELECT
        dpa.*,
        dpap.distributor_activity_unit_price,
        dpap.distributor_activity_operation_fee,
        dpap.distributor_activity_final_delivery_fee,
        dpap.distributor_activity_pick_up_price,
        dpap.distributor_activity_drop_shipping_price,
        dpas.id as stock_id,
        dpas.distributor_activity_code as stock_distributor_activity_code,
        dpas.distributor_activity_id as stock_distributor_activity_id,
        dpas.supplier_activity_stock_id,
        dpas.warehouse_system_code,
        dpas.warehouse_code,
        dpas.supported_logistics as stock_supported_logistics,
        dpas.quantity_total,
        dpas.quantity_sold,
        dpas.quantity_surplus,
        dpas.del_flag as stock_del_flag
        FROM distributor_product_activity dpa
        inner join product_sku ps on dpa.product_sku_code = ps.product_sku_code and ps.del_flag = 0
        INNER JOIN distributor_product_activity_price dpap ON dpa.id = dpap.distributor_activity_id AND dpap.del_flag = 0
        INNER JOIN distributor_product_activity_stock dpas ON dpa.id = dpas.distributor_activity_id AND dpas.del_flag = 0
        WHERE dpa.del_flag = 0
        AND ps.product_code = #{productCode}
        AND ps.shelf_state='OnShelf'
        AND dpa.site = #{site}
        AND dpa.activity_state = 'InProgress'
        AND dpas.quantity_surplus > 0
        AND dpa.exception_code = 0
        ORDER BY dpa.id DESC
    </select>

    <select id="getDistributorAvailableActivesBySupplierActiveCode"
            resultMap="DistributorProductActivityDetailsMap">
        SELECT dpa.*,
               dpap.distributor_activity_unit_price,
               dpap.distributor_activity_operation_fee,
               dpap.distributor_activity_final_delivery_fee,
               dpap.distributor_activity_pick_up_price,
               dpap.distributor_activity_drop_shipping_price,
               dpas.id                        as stock_id,
               dpas.distributor_activity_code as stock_distributor_activity_code,
               dpas.distributor_activity_id   as stock_distributor_activity_id,
               dpas.supplier_activity_stock_id,
               dpas.warehouse_system_code,
               dpas.warehouse_code,
               dpas.supported_logistics       as stock_supported_logistics,
               dpas.quantity_total,
               dpas.quantity_sold,
               dpas.quantity_surplus,
               dpas.del_flag                  as stock_del_flag
        FROM distributor_product_activity dpa
                 INNER JOIN distributor_product_activity_price dpap
                            ON dpa.id = dpap.distributor_activity_id AND dpap.del_flag = 0
                 INNER JOIN distributor_product_activity_stock dpas
                            ON dpa.id = dpas.distributor_activity_id AND dpas.del_flag = 0
        WHERE dpa.del_flag = 0
        and dpa.supplier_activity_code=#{supplierActiveCode}
        ORDER BY dpa.id DESC
    </select>
</mapper>
